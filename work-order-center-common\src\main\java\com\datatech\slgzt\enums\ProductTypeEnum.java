package com.datatech.slgzt.enums;

import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Getter;

/**
 * 商品类型枚举
 *
 * <AUTHOR>
 */

@Getter
public enum ProductTypeEnum {
    /**
     * 云产品类型
     */
    ECS("ecs", "云主机"),
    EVS("evs", "云硬盘"),
    SYS_EVS("sysEvs", "系统云硬盘"),
    SHARE_EVS("shareEvs", "共享云硬盘"),
    EIP("eip", "弹性IP"),
    MYSQL("mysql", "mysql云数据库"),
    BMS("bms", "裸金属"),
    GMS("gms", "gpu裸金属"),
    POSTGRESQL("postgreSql", "PostgreSql"),
    REDIS("redis", "REDIS"),
    OTHER("other", "其他产品"),
    GCS("gcs", "GPU云主机"),
    OBS("obs", "对象存储"),
    BUCKET("bucket", "对象存储（桶）"),
    SLB("slb", "负载均衡"),
    NAT("nat", "NAT网关"),
    VPC("vpc", "VPC"),
    BASE("base", "订单中基础信息"),
    NETWORK("network", "网络"),
    NETCARD("netcard", "多平面网络"),
    CQ("cq", "容器配额"),
    BACKUP("backup", "云备份策略"),
    CLOUDPORT("cloudport", "云端口"),
    VPN("vpn", "vpn"),
    NAS("nas", "nas"),
    PHYSICAL_MACHINE("pm", "物理机"),
    NPU_PHYSICAL_MACHINE("npm", "npu裸金属"),
    FLINK("flink", "flink"),
    KAFKA("kafka", "kafka"),
    ES("es", "es"),
    RDS_MYSQL("rdsMysql", "云数据库"),
    BLD_REDIS("bldRedis", "宝兰德redis"),
    VIP("vip", "虚拟ip"),
    UNKNOWN("unknown", "--"),
    ;

    private final String code;
    private final String desc;

    ProductTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static ProductTypeEnum getByCode(String code) {
        if (ObjNullUtils.isNotNull(code)) {
            for (ProductTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return ProductTypeEnum.UNKNOWN;
    }

}