package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.nostander.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月11日 10:18:21
 */
@Data
@Accessors(chain = true)
public class ResOpenOpm {

    private Long tenantId;

    private EcsModel escModel;

    private NpuPhysicalMachineModel npuPhysicalMachineModel;

    private EscVpcModel escVpcModel;

    private MysqlV2Model mysqlModel;

    private String subOrderId;
    private String gId;

    //客户Id
    private String customId;

    //计费号
    private String account;

    private String vmName;

    private String domainCode;

//    private String deviceType;


    //businessSystemCode
    private String businessSystemCode;


    /**
     * 挂载云硬盘数据创建模型
     */
    private List<EvsModel> evsModelList;

    /**
     * 挂载弹性公网IP数据创建模型
     */
    private List<EipModel> eipModelList;

    /**
     * 多平面网络
     */
    private List<NetcardModel> netcardModelList;

    private List<SlbModel> slbModelList;

    private List<NatGatwayModel> natGatwayModelList;

    private List<ObsModel> obsModelList;

    private List<BackupModel> backupModelList;

    private List<VpnModel> vpnModelList;

    private List<NasModel> nasModelList;
}
