package com.datatech.slgzt.model.req.standard;

import com.datatech.slgzt.model.file.UploadFileModel;
import com.datatech.slgzt.model.nostander.*;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月03日 18:43:17
 */
@Data
public class StandardWorkOrderCreateReq {


    //id
    private String id;

    //工单标题
    private String orderTitle;

    //资源申请说明
    private String orderDesc;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    /**
     * 业务系统id
     */
    private Long busiSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 三级云领导ID
     */
    private Long cloudLeaderId;

    /**
     * 二级业务部门领导ID
     */
    private Long busiDepartLeaderId;

    /**
     * 三级业务部门领导ID
     */
    private Long levelThreeLeaderId;

    /**
     * 二级云资源领导id
     */
    private Long secondLevelLeaderId;


    //资源上云说明书附件 的json
    private List<UploadFileModel> resourceApplyFiles;

    private String domainCode;

    /**
     * 云类型分类
     */
    private String catalogueDomainCode;

    //备注
    private String remark;

    /**
     * 局方负责人
     */
    private String bureauUserName;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 二级业务部门领导名称
     */
    private String businessDepartLeaderName;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;


    /**
     * Ecs申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * mysql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<EcsModel> mysqlModelList;

    /**
     * postgreSql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private List<EcsModel> postgreSqlModelList;

    private List<EcsModel> redisModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * 共享evs申请资源列表的json
     */
    private List<EvsModel> shareEvsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 容器资源配额申请json
     */
    private List<CQModel> cqModelList;

    /**
     * 备份策略申请json
     */
    private List<BackupModel> backupModelList;

    /**
     * vpn申请json
     */
    private List<VpnModel> vpnModelList;

    /**
     * nas申请json
     */
    private List<NasModel> nasModelList;
    /**
     * 物理机申请json
     */
    @JsonProperty("pmModelList")
    private List<PhysicalMachineModel> physicalMachineModelList;

    /**
     * flink申请json
     */
    private List<FlinkModel> flinkModelList;

    /**
     * kafka申请json
     */
    private List<KafkaModel> kafkaModelList;

    /**
     * es申请json
     */
    private List<EsModel> esModelList;

    /**
     * 宝兰德redis申请json
     */
    private List<BldRedisModel> bldRedisModelList;

    /**
     * npu裸金属申请json
     */
    private List<NpuPhysicalMachineModel> npmModelList;

}
