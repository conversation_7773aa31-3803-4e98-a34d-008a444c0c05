package com.datatech.slgzt.impl.service.standard.param;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.standard.ResOpenParamStrategyService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 联合开通ecs ecs部分
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月07日 17:14:29
 */
@Service
public class BmsResOpenParamParamStrategyServiceImpl implements ResOpenParamStrategyService {


    @Override
    public List<ResOpenReqModel.ProductOrder> assembleParam(ResOpenOpm opm) {
        List<ResOpenReqModel.ProductOrder> productOrders = Lists.newArrayList();
        //-----------------设置具体资源------------------------------------------------
        //-----------------1.bms部分------------------------------------------------
        ResOpenReqModel.ProductOrder pmProductOrder = new ResOpenReqModel.ProductOrder();
        NpuPhysicalMachineModel npmModel = opm.getNpuPhysicalMachineModel();
        //设置plane 目前没这个入参不知道是不是强要求
        //这个id在工单创建的时候就会存储在数据中，也可以去数据库找是一样
        pmProductOrder.setGId(npmModel.getId().toString());
        pmProductOrder.setProductOrderId(npmModel.getId().toString());
        pmProductOrder.setProductOrderType("BMS_CREATE");
        pmProductOrder.setProductType(ProductTypeEnum.NPU_PHYSICAL_MACHINE.getCode());
        pmProductOrder.setSubOrderId(opm.getSubOrderId());
        //-----------------1.1.bms-attrs部分------------------------------------------------
        ResOpenReqModel.Attrs bmsAttrs = new ResOpenReqModel.Attrs();
        bmsAttrs.setGId(opm.getGId());
        bmsAttrs.setAzCode(npmModel.getAzCode());
        bmsAttrs.setTime(npmModel.getApplyTime());
        bmsAttrs.setFlavorCode(npmModel.getFlavorCode());
        bmsAttrs.setFlavorId(npmModel.getFlavorCode());
        bmsAttrs.setImageOs(npmModel.getImageOs());
        bmsAttrs.setAdminPass(ObjNullUtils.isNull(npmModel.getPassword())?"Vrts@sec0106#":npmModel.getPassword());
        List<PlaneNetworkModel> planeNetworkModelList = npmModel.getPlaneNetworkModel();
        PlaneNetworkModel plantNetworkModel = StreamUtils.findAny(planeNetworkModelList);
        Precondition.checkArgument(plantNetworkModel, "planeNetworkModelList is empty");
        //判断是网络还是VPC
        if ("vpc".equals(plantNetworkModel.getType())) {
            EscVpcModel escVpcModel = new EscVpcModel();
            escVpcModel.setVpcId(plantNetworkModel.getId());
            ArrayList<EscVpcModel.SubnetModel> subnetModels = Lists.newArrayList();
            List<PlaneNetworkModel.Subnet> subnets = plantNetworkModel.getSubnets();
            //把subnet转换成esc的subnet
            subnets.forEach(subnet -> {
                EscVpcModel.SubnetModel escSubnetModel = new EscVpcModel.SubnetModel();
                escSubnetModel.setSubnetId(subnet.getSubnetId());
                subnetModels.add(escSubnetModel);
            });
            escVpcModel.setSubnets(subnetModels);
            bmsAttrs.setVpcInfo(escVpcModel);
        } else {
            NetworkModel networkModel = new NetworkModel();
            bmsAttrs.setPlane(plantNetworkModel.getPlane());
            networkModel.setNetworkIds(Lists.newArrayList(plantNetworkModel.getId()));
            List<PlaneNetworkModel.Subnet> subnets = plantNetworkModel.getSubnets();
            List<NetworkModel.SubnetModel> subnetModel=Lists.newArrayList();
            subnets.forEach(subnet -> {
                NetworkModel.SubnetModel subnetModel1 = new NetworkModel.SubnetModel();
                subnetModel1.setSubnetId(subnet.getSubnetId());
                subnetModel1.setIpAddress(subnet.getIpAddress());
                subnetModel.add(subnetModel1);
            });
            networkModel.setSubnets(subnetModel);
            bmsAttrs.setVpcInfo(networkModel);
        }
        bmsAttrs.setPmName(npmModel.getPmName());
        bmsAttrs.setImageId(npmModel.getImageId());
        pmProductOrder.setAttrs(bmsAttrs);
        productOrders.add(pmProductOrder);
        return productOrders;
    }


    /**
     * 注册strategy
     */
    @Override
    public ProductTypeEnum register() {
        return ProductTypeEnum.NPU_PHYSICAL_MACHINE;
    }
}
