package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 物理机模型
 * <AUTHOR>
 * @date 2025/6/23
 */
@Data
public class NpuPhysicalMachineModel extends BaseProductModel {

    /**
     * 裸金属名称
     */
    private String pmName;

    /**
     * 开通数量
     */
    private Integer openNum = 1;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 规格类型
     */
    private String flavorType;

    /**
     * 规格名称
     */
    private String flavorName;

    /**
     * 规格编码
     */
    private String flavorCode;

    /**
     * 规格id
     */
    private String flavorId;

    /**
     * 系统版本
     */
    private String imageVersion;

    /**
     * 系统版本
     */
    private String imageOs;

    /**
     * 镜像id
     */
    private String imageId;

    /**
     * 密码
     */
    private String password;

    /**
     * 网络平面
     */
    private String plane;

    private List<PlaneNetworkModel> planeNetworkModel;
}
