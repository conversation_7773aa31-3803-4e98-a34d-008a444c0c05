package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.FlavorModelDTO;
import com.datatech.slgzt.model.query.FlavorModelQuery;
import com.datatech.slgzt.model.query.FlavorQuery;
import com.datatech.slgzt.model.req.flavor.FlavorListReq;
import com.datatech.slgzt.model.req.flavor.FlavorModelListReq;
import com.datatech.slgzt.model.vo.flavor.FlavorModelTreeVO;
import com.datatech.slgzt.model.vo.flavor.FlavorModelVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multiset;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface FlavorModelWebConvert {


    FlavorModelQuery convert(FlavorModelListReq req);

    @Mapping(source = "type", target = "type" ,qualifiedByName = "type")
    @Mapping(source = "typeList", target = "typeList",qualifiedByName = "typeList")
    FlavorQuery convert(FlavorListReq req);

    FlavorModelVO convert(FlavorModelDTO flavorModel);


    @Named("typeList")
    default List<String> typeList(List<String> typeList) {
        if (ObjNullUtils.isNull(typeList)) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (String type : typeList) {
            String convertedType = type(type);
            if (convertedType != null && !convertedType.equals("UNKNOWN")) {
                result.add(convertedType);
            }
        }
        return result;
    }

    @Named("type")
    default String type(String type) {
        if (ObjNullUtils.isNull(type)) {
            return null;
        }
        switch (type) {
            case "ecs":
                return "VM";
            case "gcs":
                return "GPU";
            case "rdsMysql":
                return "RDS";
            case "slb":
                return "SLB";
            case "nat":
                return "NAT";
            case "obs":
                return "OBS";
            case "evs":
                return "evs";
            case "bms":
                return "BMS";
            default:
                return "UNKNOWN";
        }
    }

}
